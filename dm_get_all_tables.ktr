<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>dm_get_all_tables</name>
    <description>获取达梦数据库所有表名称</description>
    <extended_description></extended_description>
    <trans_version></trans_version>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection></connection>
        <schema></schema>
        <table></table>
        <size_limit_lines></size_limit_lines>
        <interval></interval>
        <timeout_days></timeout_days>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
      </trans-log-table>
    </log>
    <maxdate>
      <connection></connection>
      <table></table>
      <field></field>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file></shared_objects_file>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/30 09:00:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/07/30 09:00:00.000</modified_date>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>DM_DB</name>
    <server>localhost</server>
    <type>GENERIC</type>
    <access>Native</access>
    <database>DAMENG</database>
    <port>5236</port>
    <username>SYSDBA</username>
    <password>Encrypted 2be98afc86aa7f2e4bb18bd63c99dbdde</password>
    <servername></servername>
    <data_tablespace></data_tablespace>
    <index_tablespace></index_tablespace>
    <attributes>
      <attribute><code>CUSTOM_DRIVER_CLASS</code><attribute>dm.jdbc.driver.DmDriver</attribute></attribute>
      <attribute><code>CUSTOM_URL</code><attribute>jdbc:dm://localhost:5236</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>5236</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>Y</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>获取所有表名</from>
      <to>输出表名到文件</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>获取所有表名</name>
    <type>TableInput</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <connection>DM_DB</connection>
    <sql>SELECT TABLE_NAME FROM USER_TABLES ORDER BY TABLE_NAME</sql>
    <limit>0</limit>
    <lookup></lookup>
    <execute_each_row>N</execute_each_row>
    <variables_active>N</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>150</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>输出表名到文件</name>
    <type>TextFileOutput</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <separator>|</separator>
    <enclosure></enclosure>
    <enclosure_forced>N</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>N</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding>UTF-8</encoding>
    <endedLine></endedLine>
    <fileNameInField>N</fileNameInField>
    <fileNameField></fileNameField>
    <create>Y</create>
    <file>
      <name>table_list</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>N</do_not_open_new_file_init>
      <extention>txt</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format></date_time_format>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>N</fast_dump>
      <splitevery>0</splitevery>
    </file>
    <fields>
      <field>
        <name>TABLE_NAME</name>
        <type>String</type>
        <format></format>
        <currency></currency>
        <decimal></decimal>
        <group></group>
        <nullif></nullif>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
    </fields>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>350</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
