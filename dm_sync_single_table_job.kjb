<?xml version="1.0" encoding="UTF-8"?>
<job>
  <name>dm_sync_single_table_job</name>
  <description>单表同步作业包装器</description>
  <extended_description></extended_description>
  <job_version></job_version>
  <job_status>0</job_status>
  <directory>/</directory>
  <created_user>-</created_user>
  <created_date>2024/07/30 09:00:00.000</created_date>
  <modified_user>-</modified_user>
  <modified_date>2024/07/30 09:00:00.000</modified_date>
  <parameters>
    <parameter>
      <name>TABLE_NAME</name>
      <default_value></default_value>
      <description>要同步的表名</description>
    </parameter>
  </parameters>
  <slaveservers>
  </slaveservers>
  <job-log-table>
    <connection></connection>
    <schema></schema>
    <table></table>
    <size_limit_lines></size_limit_lines>
    <interval></interval>
    <timeout_days></timeout_days>
    <field>
      <id>ID_JOB</id>
      <enabled>Y</enabled>
      <name>ID_JOB</name>
    </field>
    <field>
      <id>JOBNAME</id>
      <enabled>Y</enabled>
      <name>JOBNAME</name>
    </field>
    <field>
      <id>STATUS</id>
      <enabled>Y</enabled>
      <name>STATUS</name>
    </field>
    <field>
      <id>LINES_READ</id>
      <enabled>Y</enabled>
      <name>LINES_READ</name>
    </field>
    <field>
      <id>LINES_WRITTEN</id>
      <enabled>Y</enabled>
      <name>LINES_WRITTEN</name>
    </field>
    <field>
      <id>ERRORS</id>
      <enabled>Y</enabled>
      <name>ERRORS</name>
    </field>
    <field>
      <id>STARTDATE</id>
      <enabled>Y</enabled>
      <name>STARTDATE</name>
    </field>
    <field>
      <id>ENDDATE</id>
      <enabled>Y</enabled>
      <name>ENDDATE</name>
    </field>
  </job-log-table>
  <pass_batchid>N</pass_batchid>
  <shared_objects_file></shared_objects_file>
  <entries>
    <entry>
      <name>START</name>
      <description></description>
      <type>SPECIAL</type>
      <start>Y</start>
      <dummy>N</dummy>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>0</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>100</xloc>
      <yloc>100</yloc>
    </entry>
    <entry>
      <name>同步单表</name>
      <description></description>
      <type>TRANS</type>
      <specification_method>filename</specification_method>
      <trans_object_id></trans_object_id>
      <filename>dm_sync_single_table.ktr</filename>
      <transname></transname>
      <arg_from_previous>N</arg_from_previous>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile></logfile>
      <logext></logext>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name></slave_server_name>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
        <parameter>
          <name>TABLE_NAME</name>
          <stream_name></stream_name>
          <value>${TABLE_NAME}</value>
        </parameter>
      </parameters>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>300</xloc>
      <yloc>100</yloc>
    </entry>
  </entries>
  <hops>
    <hop>
      <from>START</from>
      <to>同步单表</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
</job>
