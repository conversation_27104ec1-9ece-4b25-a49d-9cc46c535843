<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>dm_to_mysql_sync_simple</name>
    <description>达梦数据库同步到MySQL脚本</description>
    <extended_description></extended_description>
    <trans_version></trans_version>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>TABLE_NAME</name>
        <default_value>mpe_config</default_value>
        <description>要同步的表名</description>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection></connection>
        <schema></schema>
        <table></table>
        <size_limit_lines></size_limit_lines>
        <interval></interval>
        <timeout_days></timeout_days>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject></subject>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject></subject>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject></subject>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject></subject>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject></subject>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject></subject>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
      </trans-log-table>
    </log>
    <maxdate>
      <connection></connection>
      <table></table>
      <field></field>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file></shared_objects_file>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/30 09:00:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/07/30 09:00:00.000</modified_date>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>DM_DB</name>
    <server>localhost</server>
    <type>GENERIC</type>
    <access>Native</access>
    <database>DAMENG</database>
    <port>5236</port>
    <username>SYSDBA</username>
    <password>Encrypted 2be98afc86aa7f2e4bb18bd63c99dbdde</password>
    <servername></servername>
    <data_tablespace></data_tablespace>
    <index_tablespace></index_tablespace>
    <attributes>
      <attribute><code>CUSTOM_DRIVER_CLASS</code><attribute>dm.jdbc.driver.DmDriver</attribute></attribute>
      <attribute><code>CUSTOM_URL</code><attribute>jdbc:dm://localhost:5236</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>5236</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>Y</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <connection>
    <name>MYSQL_DB</name>
    <server>localhost</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>sync_db</database>
    <port>3306</port>
    <username>root</username>
    <password>Encrypted 2be98afc86aa7f2e4bb18bd63c99dbdde</password>
    <servername></servername>
    <data_tablespace></data_tablespace>
    <index_tablespace></index_tablespace>
    <attributes>
      <attribute><code>EXTRA_OPTION_MYSQL.defaultFetchSize</code><attribute>500</attribute></attribute>
      <attribute><code>EXTRA_OPTION_MYSQL.useCursorFetch</code><attribute>true</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_LOWERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>FORCE_IDENTIFIERS_TO_UPPERCASE</code><attribute>N</attribute></attribute>
      <attribute><code>IS_CLUSTERED</code><attribute>N</attribute></attribute>
      <attribute><code>PORT_NUMBER</code><attribute>3306</attribute></attribute>
      <attribute><code>PRESERVE_RESERVED_WORD_CASE</code><attribute>Y</attribute></attribute>
      <attribute><code>QUOTE_ALL_FIELDS</code><attribute>N</attribute></attribute>
      <attribute><code>STREAM_RESULTS</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_BOOLEAN_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>SUPPORTS_TIMESTAMP_DATA_TYPE</code><attribute>Y</attribute></attribute>
      <attribute><code>USE_POOLING</code><attribute>N</attribute></attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>从达梦读取数据</from>
      <to>写入MySQL</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>从达梦读取数据</name>
    <type>TableInput</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <connection>DM_DB</connection>
    <sql>SELECT * FROM ${TABLE_NAME}</sql>
    <limit>0</limit>
    <lookup></lookup>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>150</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>写入MySQL</name>
    <type>TableOutput</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <connection>MYSQL_DB</connection>
    <schema></schema>
    <table>${TABLE_NAME}</table>
    <commit>1000</commit>
    <truncate>Y</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>N</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field></partitioning_field>
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field></tablename_field>
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field></return_field>
    <fields>
    </fields>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>350</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
