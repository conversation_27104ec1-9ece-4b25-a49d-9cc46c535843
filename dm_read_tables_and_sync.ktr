<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>dm_read_tables_and_sync</name>
    <description>读取表名文件并逐个同步</description>
    <extended_description></extended_description>
    <trans_version></trans_version>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection></connection>
        <schema></schema>
        <table></table>
        <size_limit_lines></size_limit_lines>
        <interval></interval>
        <timeout_days></timeout_days>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
      </trans-log-table>
    </log>
    <maxdate>
      <connection></connection>
      <table></table>
      <field></field>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file></shared_objects_file>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2024/07/30 09:00:00.000</created_date>
    <modified_user>-</modified_user>
    <modified_date>2024/07/30 09:00:00.000</modified_date>
  </info>
  <notepads>
  </notepads>
  <order>
    <hop>
      <from>读取表名文件</from>
      <to>执行同步作业</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>读取表名文件</name>
    <type>TextFileInput</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <filename>table_list.txt</filename>
    <filemask></filemask>
    <exclude_filemask></exclude_filemask>
    <file_required>N</file_required>
    <include_subfolders>N</include_subfolders>
    <filetype>CSV</filetype>
    <separator>|</separator>
    <enclosure></enclosure>
    <enclosure_breaks>N</enclosure_breaks>
    <escapechar></escapechar>
    <header>N</header>
    <nr_headerlines>0</nr_headerlines>
    <footer>N</footer>
    <nr_footerlines>0</nr_footerlines>
    <line_wrapped>N</line_wrapped>
    <nr_wraps>1</nr_wraps>
    <layout_paged>N</layout_paged>
    <nr_lines_per_page>80</nr_lines_per_page>
    <nr_lines_doc_header>0</nr_lines_doc_header>
    <noempty>Y</noempty>
    <include_filename>N</include_filename>
    <filename_field></filename_field>
    <include_rownum>N</include_rownum>
    <rownum_field></rownum_field>
    <format>DOS</format>
    <encoding>UTF-8</encoding>
    <length>Characters</length>
    <add_to_result_filenames>Y</add_to_result_filenames>
    <file>
      <name>table_list.txt</name>
      <filemask></filemask>
      <exclude_filemask></exclude_filemask>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>TABLE_NAME</name>
        <type>String</type>
        <format></format>
        <currency></currency>
        <decimal></decimal>
        <group></group>
        <nullif></nullif>
        <ifnull></ifnull>
        <position>-1</position>
        <length>100</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
      </field>
    </fields>
    <limit>0</limit>
    <error_ignored>N</error_ignored>
    <skip_bad_files>N</skip_bad_files>
    <file_error_field></file_error_field>
    <file_error_message_field></file_error_message_field>
    <error_line_skipped>N</error_line_skipped>
    <error_count_field></error_count_field>
    <error_fields_field></error_fields_field>
    <error_text_field></error_text_field>
    <bad_line_files_dest_dir></bad_line_files_dest_dir>
    <bad_line_files_extension>errors</bad_line_files_extension>
    <error_line_files_dest_dir></error_line_files_dest_dir>
    <error_line_files_extension>errors</error_line_files_extension>
    <line_number_files_dest_dir></line_number_files_dest_dir>
    <line_number_files_extension>line</line_number_files_extension>
    <date_format_lenient>N</date_format_lenient>
    <date_format_locale>en_US</date_format_locale>
    <shortFileFieldName></shortFileFieldName>
    <pathFieldName></pathFieldName>
    <hiddenFieldName></hiddenFieldName>
    <lastModificationTimeFieldName></lastModificationTimeFieldName>
    <uriNameFieldName></uriNameFieldName>
    <rootUriNameFieldName></rootUriNameFieldName>
    <extensionFieldName></extensionFieldName>
    <sizeFieldName></sizeFieldName>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>150</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>执行同步作业</name>
    <type>JobExecutor</type>
    <description></description>
    <distribute>Y</distribute>
    <custom_distribution></custom_distribution>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name></schema_name>
    </partitioning>
    <specification_method>filename</specification_method>
    <job_object_id></job_object_id>
    <filename>dm_sync_single_table_job.kjb</filename>
    <jobname></jobname>
    <arg_from_previous>N</arg_from_previous>
    <params_from_previous>N</params_from_previous>
    <exec_per_row>Y</exec_per_row>
    <set_logfile>N</set_logfile>
    <logfile></logfile>
    <logext></logext>
    <add_date>N</add_date>
    <add_time>N</add_time>
    <loglevel>Basic</loglevel>
    <wait_until_finished>Y</wait_until_finished>
    <follow_abort_remote>N</follow_abort_remote>
    <create_parent_folder>N</create_parent_folder>
    <logging_remote_work>N</logging_remote_work>
    <run_configuration></run_configuration>
    <parameters>
      <pass_all_parameters>Y</pass_all_parameters>
      <parameter>
        <name>TABLE_NAME</name>
        <stream_name>TABLE_NAME</stream_name>
        <value></value>
      </parameter>
    </parameters>
    <execution_result_target_step></execution_result_target_step>
    <execution_time_field>ExecutionTime</execution_time_field>
    <execution_result_field>ExecutionResult</execution_result_field>
    <execution_errors_field>ExecutionNrErrors</execution_errors_field>
    <execution_lines_read_field>ExecutionLinesRead</execution_lines_read_field>
    <execution_lines_written_field>ExecutionLinesWritten</execution_lines_written_field>
    <execution_lines_input_field>ExecutionLinesInput</execution_lines_input_field>
    <execution_lines_output_field>ExecutionLinesOutput</execution_lines_output_field>
    <execution_lines_rejected_field>ExecutionLinesRejected</execution_lines_rejected_field>
    <execution_lines_updated_field>ExecutionLinesUpdated</execution_lines_updated_field>
    <execution_lines_deleted_field>ExecutionLinesDeleted</execution_lines_deleted_field>
    <execution_files_retrieved_field>ExecutionFilesRetrieved</execution_files_retrieved_field>
    <execution_exit_status_field>ExecutionExitStatus</execution_exit_status_field>
    <execution_log_text_field>ExecutionLogText</execution_log_text_field>
    <execution_log_channelid_field>ExecutionLogChannelId</execution_log_channelid_field>
    <groupSize></groupSize>
    <groupField></groupField>
    <groupTime></groupTime>
    <cluster_schema></cluster_schema>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>350</xloc>
      <yloc>100</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
</transformation>
